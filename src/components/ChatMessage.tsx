import React from 'react';
import { Card } from '@/components/ui/card';
import ScriptureCard from './ScriptureCard';

interface ScripturePassage {
  faith: string;
  book: string;
  chapter: number;
  verse: string;
  text: string;
  citation: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  scripture?: ScripturePassage[];
}

interface ChatMessageProps {
  message: Message;
  scriptures?: ScripturePassage[];
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, scriptures }) => {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-2xl ${isUser ? 'w-auto' : 'w-full'}`}>
        {/* Message Header */}
        {isAssistant && (
          <div className="flex items-center gap-2 mb-2">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-red-600 to-red-700 flex items-center justify-center shadow-lg shadow-red-500/30">
              <span className="text-xs font-semibold text-white">Λ</span>
            </div>
            <span className="text-sm font-medium text-red-100">Logos</span>
            <span className="text-xs text-red-400/70">
              {formatTime(message.timestamp)}
            </span>
          </div>
        )}

        {/* Scripture Passages */}
        {scriptures && scriptures.length > 0 && (
          <div className="space-y-3 mb-4">
            {scriptures.map((scripture, index) => (
              <ScriptureCard key={index} scripture={scripture} />
            ))}
          </div>
        )}

        {/* Message Content */}
        <Card
          className={`
            p-4 shadow-lg
            ${isUser
              ? 'bg-gradient-to-r from-red-600 to-red-700 text-white ml-12 shadow-red-500/30'
              : 'bg-black/40 border-red-900/30 shadow-red-500/20'
            }
          `}
        >
          <div className={`
            ${isUser ? 'text-white' : 'text-red-100'}
            ${isAssistant && scriptures ? 'font-scripture' : 'font-interface'}
            leading-relaxed
          `}>
            {message.content}
          </div>

          {isUser && (
            <div className="text-xs text-white/70 mt-2">
              {formatTime(message.timestamp)}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ChatMessage;